#!/usr/bin/env python3
"""
Testnet System Validation
Comprehensive validation of all system components on testnet without requiring keypair signing.
"""

import os
import sys
import asyncio
import logging
import json
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestnetSystemValidator:
    """Comprehensive testnet system validation."""
    
    def __init__(self):
        """Initialize validator."""
        self.results = {}
        self.wallet_address = None
        
    async def run_complete_validation(self):
        """Run complete testnet system validation."""
        logger.info("🧪 TESTNET SYSTEM VALIDATION")
        logger.info("=" * 60)
        
        # Load testnet environment
        load_dotenv('.env.testnet')
        self.wallet_address = os.getenv('WALLET_ADDRESS')
        
        tests = [
            ("Environment Setup", self.test_environment_setup),
            ("Network Connectivity", self.test_network_connectivity),
            ("Core Module Imports", self.test_core_module_imports),
            ("Configuration Loading", self.test_configuration_loading),
            ("RPC Client Functionality", self.test_rpc_client_functionality),
            ("Transaction Builder", self.test_transaction_builder),
            ("Signal Generation", self.test_signal_generation),
            ("Risk Management", self.test_risk_management),
            ("Telegram Notifications", self.test_telegram_notifications),
            ("Dashboard Components", self.test_dashboard_components),
            ("Data Sources", self.test_data_sources),
            ("System Integration", self.test_system_integration)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n🔍 Testing: {test_name}")
            try:
                result = await test_func()
                if result:
                    logger.info(f"✅ {test_name}: PASSED")
                    passed += 1
                else:
                    logger.error(f"❌ {test_name}: FAILED")
                self.results[test_name] = result
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")
                self.results[test_name] = False
        
        # Generate report
        success_rate = (passed / total) * 100
        logger.info("\n" + "=" * 60)
        logger.info("🎯 TESTNET VALIDATION RESULTS")
        logger.info("=" * 60)
        logger.info(f"Tests Passed: {passed}/{total} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            logger.info("✅ TESTNET SYSTEM READY FOR TRANSACTION TESTING")
        elif success_rate >= 60:
            logger.info("⚠️ TESTNET SYSTEM MOSTLY READY - MINOR ISSUES")
        else:
            logger.info("❌ TESTNET SYSTEM NEEDS FIXES BEFORE TESTING")
        
        return success_rate >= 60
    
    async def test_environment_setup(self):
        """Test environment setup."""
        required_vars = [
            'WALLET_ADDRESS', 'HELIUS_API_KEY', 'SOLANA_NETWORK',
            'TRADING_ENABLED', 'TESTNET_MODE'
        ]
        
        for var in required_vars:
            if not os.getenv(var):
                logger.error(f"Missing environment variable: {var}")
                return False
        
        if os.getenv('SOLANA_NETWORK') != 'testnet':
            logger.error("SOLANA_NETWORK should be 'testnet'")
            return False
        
        return True
    
    async def test_network_connectivity(self):
        """Test testnet network connectivity."""
        try:
            import httpx
            
            async with httpx.AsyncClient() as client:
                # Test testnet RPC
                response = await client.post(
                    "https://api.testnet.solana.com",
                    json={"jsonrpc": "2.0", "id": 1, "method": "getHealth"},
                    timeout=10.0
                )
                
                if response.status_code != 200:
                    return False
                
                # Test wallet balance check
                balance_response = await client.post(
                    "https://api.testnet.solana.com",
                    json={
                        "jsonrpc": "2.0", "id": 1, "method": "getBalance",
                        "params": [self.wallet_address]
                    },
                    timeout=10.0
                )
                
                return balance_response.status_code == 200
                
        except Exception as e:
            logger.error(f"Network connectivity error: {e}")
            return False
    
    async def test_core_module_imports(self):
        """Test core module imports."""
        try:
            from phase_4_deployment.rpc_execution.helius_client import HeliusClient
            from phase_4_deployment.rpc_execution.transaction_executor import TransactionExecutor
            from phase_4_deployment.rpc_execution.tx_builder import TxBuilder
            from core.notifications.telegram_notifier import TelegramNotifier
            from core.risk.risk_manager import RiskManager
            from core.risk.position_sizer import PositionSizer
            return True
        except ImportError as e:
            logger.error(f"Module import error: {e}")
            return False
    
    async def test_configuration_loading(self):
        """Test configuration loading."""
        try:
            # Test main config
            import yaml
            with open('config.yaml', 'r') as f:
                config = yaml.safe_load(f)
            
            # Test testnet config
            testnet_config_path = Path('config/environments/testnet.yaml')
            if testnet_config_path.exists():
                with open(testnet_config_path, 'r') as f:
                    testnet_config = yaml.safe_load(f)
                return True
            else:
                logger.warning("Testnet config not found")
                return True  # Not critical
                
        except Exception as e:
            logger.error(f"Configuration loading error: {e}")
            return False
    
    async def test_rpc_client_functionality(self):
        """Test RPC client functionality."""
        try:
            from phase_4_deployment.rpc_execution.helius_client import HeliusClient
            
            client = HeliusClient(api_key=os.getenv('HELIUS_API_KEY'))
            
            # Test basic functionality without actual calls
            if hasattr(client, 'get_balance') and hasattr(client, 'send_transaction'):
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"RPC client error: {e}")
            return False
    
    async def test_transaction_builder(self):
        """Test transaction builder."""
        try:
            from phase_4_deployment.rpc_execution.tx_builder import TxBuilder
            
            # Create builder without keypair
            builder = TxBuilder(self.wallet_address)
            
            # Test signal creation
            test_signal = {
                "action": "BUY",
                "market": "SOL-USDC",
                "price": 180.0,
                "size": 0.001,
                "confidence": 0.8
            }
            
            # Test that builder can be created and has required methods
            return hasattr(builder, 'build_and_sign_transaction')
            
        except Exception as e:
            logger.error(f"Transaction builder error: {e}")
            return False
    
    async def test_signal_generation(self):
        """Test signal generation."""
        try:
            # Test basic signal structure
            test_signal = {
                "action": "BUY",
                "market": "SOL-USDC",
                "price": 180.0,
                "size": 0.001,
                "confidence": 0.8,
                "timestamp": datetime.now().isoformat()
            }
            
            # Validate signal structure
            required_fields = ['action', 'market', 'price', 'size', 'confidence']
            for field in required_fields:
                if field not in test_signal:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Signal generation error: {e}")
            return False
    
    async def test_risk_management(self):
        """Test risk management components."""
        try:
            from core.risk.risk_manager import RiskManager
            from core.risk.position_sizer import PositionSizer
            
            # Test risk manager creation
            risk_manager = RiskManager()
            
            # Test position sizer creation
            position_sizer = PositionSizer()
            
            return True
            
        except Exception as e:
            logger.error(f"Risk management error: {e}")
            return False
    
    async def test_telegram_notifications(self):
        """Test Telegram notifications."""
        try:
            from core.notifications.telegram_notifier import TelegramNotifier
            
            notifier = TelegramNotifier()
            
            if notifier.enabled:
                # Send test message
                import httpx
                
                bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
                chat_id = os.getenv('TELEGRAM_CHAT_ID')
                
                if bot_token and chat_id:
                    async with httpx.AsyncClient() as client:
                        response = await client.post(
                            f"https://api.telegram.org/bot{bot_token}/sendMessage",
                            json={
                                "chat_id": chat_id,
                                "text": "🧪 Testnet system validation in progress...\n\n📍 All core components functional\n🌐 Network: Testnet\n⏰ " + datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            },
                            timeout=10.0
                        )
                        return response.status_code == 200
                else:
                    logger.warning("Telegram credentials not found")
                    return True  # Not critical
            else:
                logger.warning("Telegram notifier disabled")
                return True  # Not critical
                
        except Exception as e:
            logger.error(f"Telegram notification error: {e}")
            return False
    
    async def test_dashboard_components(self):
        """Test dashboard components."""
        try:
            # Check if dashboard files exist
            dashboard_path = Path('phase_4_deployment/dashboard/streamlit_dashboard.py')
            if dashboard_path.exists():
                return True
            else:
                logger.warning("Dashboard file not found")
                return False
                
        except Exception as e:
            logger.error(f"Dashboard component error: {e}")
            return False
    
    async def test_data_sources(self):
        """Test data sources."""
        try:
            # Test that we can access testnet data
            import httpx
            
            async with httpx.AsyncClient() as client:
                # Test getting latest blockhash
                response = await client.post(
                    "https://api.testnet.solana.com",
                    json={
                        "jsonrpc": "2.0", "id": 1,
                        "method": "getLatestBlockhash"
                    },
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return 'result' in data
                
                return False
                
        except Exception as e:
            logger.error(f"Data sources error: {e}")
            return False
    
    async def test_system_integration(self):
        """Test system integration."""
        try:
            # Test that all components can work together
            test_components = [
                'environment_setup', 'network_connectivity', 'core_module_imports',
                'rpc_client_functionality', 'transaction_builder'
            ]
            
            # Check if critical components passed
            critical_passed = all(
                self.results.get(comp.replace('_', ' ').title(), False)
                for comp in ['Environment Setup', 'Network Connectivity', 'Core Module Imports']
            )
            
            return critical_passed
            
        except Exception as e:
            logger.error(f"System integration error: {e}")
            return False

async def main():
    """Main function."""
    validator = TestnetSystemValidator()
    success = await validator.run_complete_validation()
    
    if success:
        logger.info("\n🚀 NEXT STEPS FOR TESTNET TRADING:")
        logger.info("1. Request SOL from faucet: https://faucet.solana.com/")
        logger.info("2. Fix keypair format for transaction signing")
        logger.info("3. Run actual testnet trading with real transactions")
        logger.info("4. Monitor on testnet explorer: https://explorer.solana.com/?cluster=testnet")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
