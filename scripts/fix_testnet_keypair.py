#!/usr/bin/env python3
"""
Fix Testnet Keypair Format
Ensures the testnet keypair is in the correct format for the trading system.
"""

import os
import sys
import json
import logging
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_testnet_keypair():
    """Fix the testnet keypair format by creating a new compatible one."""
    logger.info("🔧 Creating new testnet keypair with correct format...")

    try:
        from solders.keypair import Keypair

        keypair_path = Path("wallet/testnet_keypair.json")

        # Create a completely new keypair to avoid format issues
        logger.info("🆕 Generating new testnet keypair...")
        keypair = Keypair()

        # Get the public key
        pubkey = str(keypair.pubkey())
        logger.info(f"📍 New testnet wallet address: {pubkey}")

        # Save the keypair in the correct format
        # Use the full 64-byte format that the trading system expects
        full_keypair = bytes(keypair)  # Full 64 bytes
        full_keypair_array = list(full_keypair)

        with open(keypair_path, 'w') as f:
            json.dump(full_keypair_array, f)

        # Set proper permissions
        os.chmod(keypair_path, 0o600)

        logger.info("✅ New testnet keypair created and saved")
        logger.info(f"📊 Keypair format: {len(full_keypair_array)} bytes (full keypair)")

        # Test the keypair loading
        test_keypair = Keypair.from_bytes(bytes(full_keypair_array))
        test_pubkey = str(test_keypair.pubkey())

        if test_pubkey == pubkey:
            logger.info("✅ Keypair loading test successful")
            return pubkey
        else:
            logger.error("❌ Keypair loading test failed")
            return None

    except ImportError:
        logger.error("❌ solders library not available")
        return None
    except Exception as e:
        logger.error(f"❌ Error creating keypair: {e}")
        return None

def test_keypair_loading():
    """Test keypair loading with the trading system."""
    logger.info("🧪 Testing keypair loading with trading system...")

    try:
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv('.env.testnet')

        # Test the keypair loading logic from unified_live_trading
        keypair_path = os.getenv('KEYPAIR_PATH', 'wallet/testnet_keypair.json')

        if not os.path.exists(keypair_path):
            logger.error(f"❌ Keypair file not found: {keypair_path}")
            return False

        from solders.keypair import Keypair

        # Try the same loading logic as unified_live_trading
        with open(keypair_path, 'r') as f:
            keypair_data = json.load(f)

        if isinstance(keypair_data, list) and len(keypair_data) in [32, 64]:
            if len(keypair_data) == 64:
                keypair_bytes = bytes(keypair_data)
                keypair = Keypair.from_bytes(keypair_bytes)
            else:
                keypair_bytes = bytes(keypair_data)
                keypair = Keypair.from_bytes(keypair_bytes)

            pubkey = str(keypair.pubkey())
            logger.info(f"✅ Trading system keypair loading successful")
            logger.info(f"📍 Public key: {pubkey}")

            # Verify this matches the expected wallet address
            expected_address = os.getenv('WALLET_ADDRESS')
            if pubkey == expected_address:
                logger.info("✅ Keypair matches expected wallet address")
                return True
            else:
                logger.warning(f"⚠️ Keypair mismatch - Expected: {expected_address}, Got: {pubkey}")

                # Update the environment file with the correct address
                env_path = Path('.env.testnet')
                if env_path.exists():
                    with open(env_path, 'r') as f:
                        env_content = f.read()

                    # Replace the wallet addresses
                    env_content = env_content.replace(expected_address, pubkey)

                    with open(env_path, 'w') as f:
                        f.write(env_content)

                    logger.info("✅ Updated .env.testnet with correct wallet address")
                    return True
        else:
            logger.error(f"❌ Invalid keypair format: {type(keypair_data)} with length {len(keypair_data) if isinstance(keypair_data, list) else 'N/A'}")
            return False

    except Exception as e:
        logger.error(f"❌ Error testing keypair loading: {e}")
        return False

def main():
    """Main function."""
    logger.info("🔧 TESTNET KEYPAIR REPAIR TOOL")
    logger.info("=" * 50)

    # Step 1: Create new keypair
    new_pubkey = fix_testnet_keypair()
    if not new_pubkey:
        logger.error("❌ Failed to create new keypair")
        return False

    # Step 2: Update environment file with new address
    try:
        env_path = Path('.env.testnet')
        if env_path.exists():
            with open(env_path, 'r') as f:
                env_content = f.read()

            # Find and replace the old wallet address
            lines = env_content.split('\n')
            updated_lines = []

            for line in lines:
                if line.startswith('TESTNET_WALLET_ADDRESS=') or line.startswith('WALLET_ADDRESS='):
                    key = line.split('=')[0]
                    updated_lines.append(f"{key}={new_pubkey}")
                else:
                    updated_lines.append(line)

            # Write back the updated content
            with open(env_path, 'w') as f:
                f.write('\n'.join(updated_lines))

            logger.info("✅ Updated .env.testnet with new wallet address")
        else:
            logger.warning("⚠️ .env.testnet file not found")

    except Exception as e:
        logger.error(f"❌ Error updating environment file: {e}")
        return False

    # Step 3: Test keypair loading
    if not test_keypair_loading():
        logger.error("❌ Failed keypair loading test")
        return False

    logger.info("✅ Testnet keypair repair completed successfully!")
    logger.info(f"📍 New testnet wallet: {new_pubkey}")
    logger.info("🚀 Ready to run testnet trading tests")
    logger.info("💰 Don't forget to request SOL from: https://faucet.solana.com/")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
